#Requires AutoHotkey v2
; 引入UIA库
#include UIA-v2-1.1.0\Lib\UIA.ahk

/*
UIA-v2 简单测试脚本
用于验证UIA库是否正常工作
*/

; 测试UIA是否正常加载
TestUIA() {
    try {
        ; 测试1：检查UIA是否可用
        MsgBox("测试1：UIA库加载测试")
        
        ; 获取桌面元素
        desktop := UIA.GetRootElement()
        MsgBox("✓ UIA库加载成功！`n桌面元素名称: " . desktop.Name)
        
        ; 测试2：记事本自动化
        MsgBox("测试2：启动记事本进行自动化测试")
        
        ; 启动记事本
        Run "notepad.exe"
        WinWaitActive "ahk_exe notepad.exe"
        Sleep 1000  ; 等待窗口完全加载
        
        ; 获取记事本窗口元素
        npEl := UIA.ElementFromHandle("ahk_exe notepad.exe")
        MsgBox("✓ 成功获取记事本窗口元素！`n窗口名称: " . npEl.Name)
        
        ; 查找编辑区域
        try {
            ; 尝试查找Document类型
            documentEl := npEl.FindElement({Type:"Document"})
            MsgBox("✓ 找到Document元素: " . documentEl.Name)
        } catch {
            try {
                ; 如果找不到Document，尝试Edit类型
                documentEl := npEl.FindElement({Type:"Edit"})
                MsgBox("✓ 找到Edit元素: " . documentEl.Name)
            } catch {
                ; 如果都找不到，显示所有子元素
                MsgBox("未找到Document或Edit元素，显示所有子元素类型...")
                children := npEl.FindElements({})
                types := ""
                for child in children {
                    types .= child.Type . " - " . child.Name . "`n"
                }
                MsgBox("子元素列表:`n" . types)
                return
            }
        }
        
        ; 高亮显示找到的元素
        documentEl.Highlight()
        MsgBox("✓ 元素已高亮显示")
        
        ; 设置文本
        documentEl.Value := "UIA测试成功！"
        MsgBox("✓ 成功设置文本内容")
        
        ; 测试完成
        MsgBox("🎉 所有测试完成！UIA库工作正常。")
        
    } catch Error as e {
        MsgBox("❌ 测试失败: " . e.Message . "`n`n错误详情:`n" . e.Stack)
    }
}

; 运行测试
TestUIA()
