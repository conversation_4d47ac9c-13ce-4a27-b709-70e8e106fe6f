#Requires AutoHotkey v2
#include UIA-v2-1.1.0\Lib\UIA.ahk

/*
UIA 简单界面监控器
专门监控记事本的界面变化
功能：
1. 监控焦点变化
2. 监控文本内容变化
3. 监控窗口状态变化
4. 实时日志显示
*/

; 全局变量
global 监控窗口
global 日志显示
global 监控状态 := false
global 上次文本内容 := ""
global 上次焦点元素 := ""

; 创建监控界面
CreateMonitorGUI() {
    global 监控窗口 := Gui("+Resize", "UIA 简单界面监控器")
    监控窗口.MarginX := 10
    监控窗口.MarginY := 10
    
    ; 标题
    监控窗口.Add("Text", "x10 y10 w400 Center", "UIA 界面变化监控器").SetFont("s14 Bold")
    
    ; 说明
    监控窗口.Add("Text", "x10 y40 w400", "此程序将监控记事本的界面变化，请先启动记事本。")
    
    ; 控制按钮
    startBtn := 监控窗口.Add("Button", "x10 y70 w80 h30", "开始监控")
    stopBtn := 监控窗口.Add("Button", "x100 y70 w80 h30", "停止监控")
    clearBtn := 监控窗口.Add("Button", "x190 y70 w80 h30", "清空日志")
    testBtn := 监控窗口.Add("Button", "x280 y70 w80 h30", "测试记事本")
    
    ; 状态显示
    statusLabel := 监控窗口.Add("Text", "x10 y110 w400", "状态: 未开始监控")
    statusLabel.SetFont("s10 Bold")
    
    ; 日志显示
    监控窗口.Add("Text", "x10 y140 w100", "监控日志:")
    global 日志显示 := 监控窗口.Add("Edit", "x10 y160 w400 h250 VScroll ReadOnly")
    
    ; 事件绑定
    startBtn.OnEvent("Click", StartMonitoring)
    stopBtn.OnEvent("Click", StopMonitoring)
    clearBtn.OnEvent("Click", ClearLog)
    testBtn.OnEvent("Click", TestNotepad)
    
    监控窗口.OnEvent("Close", (*) => ExitApp())
    监控窗口.Show("w420 h430")
}

; 开始监控
StartMonitoring(*) {
    global 监控状态
    
    if (监控状态) {
        AddLog("监控已在运行中...")
        return
    }
    
    ; 检查记事本是否存在
    if (!WinExist("ahk_exe notepad.exe")) {
        MsgBox("请先启动记事本！")
        return
    }
    
    try {
        监控状态 := true
        ; 创建定时器，每500毫秒检查一次变化
        SetTimer(CheckChanges, 500)
        
        ; 更新状态
        for control in 监控窗口 {
            if (control.Text = "状态: 未开始监控") {
                control.Text := "状态: 正在监控记事本..."
                control.SetFont("s10 Bold", "Green")
                break
            }
        }
        
        AddLog("开始监控记事本界面变化")
        
        ; 初始化状态
        InitializeState()
        
    } catch Error as e {
        MsgBox("启动监控失败: " . e.Message)
        AddLog("启动监控失败: " . e.Message)
    }
    return
}

; 停止监控
StopMonitoring(*) {
    global 监控状态
    
    if (!监控状态) {
        AddLog("监控未在运行...")
        return
    }
    
    try {
        监控状态 := false
        ; 停止定时器
        SetTimer(CheckChanges, 0)
        
        ; 更新状态
        for control in 监控窗口 {
            if (control.Text = "状态: 正在监控记事本...") {
                control.Text := "状态: 监控已停止"
                control.SetFont("s10 Bold", "Red")
                break
            }
        }
        
        AddLog("监控已停止")
        
    } catch Error as e {
        MsgBox("停止监控失败: " . e.Message)
        AddLog("停止监控失败: " . e.Message)
    }
}

; 初始化状态
InitializeState() {
    global 上次文本内容, 上次焦点元素
    
    try {
        ; 获取记事本元素
        npEl := UIA.ElementFromHandle("ahk_exe notepad.exe")
        
        ; 获取初始文本内容
        try {
            docEl := npEl.FindElement([{Type:"Document"}, {Type:"Edit"}])
            上次文本内容 := docEl.Value ? docEl.Value : ""
        } catch {
            上次文本内容 := ""
        }
        
        ; 获取初始焦点
        try {
            focusEl := UIA.GetFocusedElement()
            上次焦点元素 := focusEl.Name ? focusEl.Name : "未知元素"
        } catch {
            上次焦点元素 := ""
        }
        
        AddLog("初始状态已记录")
        
    } catch Error as e {
        AddLog("初始化状态失败: " . e.Message)
    }
}

; 检查变化
CheckChanges() {
    global 上次文本内容, 上次焦点元素
    
    if (!监控状态) {
        return
    }
    
    try {
        ; 检查记事本是否还存在
        if (!WinExist("ahk_exe notepad.exe")) {
            AddLog("⚠️ 记事本窗口已关闭")
            StopMonitoring()
            return
        }
        
        ; 获取记事本元素
        npEl := UIA.ElementFromHandle("ahk_exe notepad.exe")
        
        ; 1. 检查文本内容变化
        CheckTextChanges(npEl)
        
        ; 2. 检查焦点变化
        CheckFocusChanges()
        
        ; 3. 检查窗口状态变化
        CheckWindowChanges(npEl)
        
    } catch Error as e {
        AddLog("检查变化时出错: " . e.Message)
    }
}

; 检查文本内容变化
CheckTextChanges(npEl) {
    global 上次文本内容
    
    try {
        docEl := npEl.FindElement([{Type:"Document"}, {Type:"Edit"}])
        当前文本 := docEl.Value ? docEl.Value : ""
        
        if (当前文本 != 上次文本内容) {
            if (上次文本内容 = "") {
                AddLog("📝 文本内容初始化: " . StrLen(当前文本) . " 个字符")
            } else {
                AddLog("📝 文本内容变化: " . StrLen(上次文本内容) . " → " . StrLen(当前文本) . " 个字符")
                if (StrLen(当前文本) > StrLen(上次文本内容)) {
                    AddLog("   ➕ 新增内容: '" . SubStr(当前文本, StrLen(上次文本内容) + 1) . "'")
                }
            }
            上次文本内容 := 当前文本
        }
        
    } catch {
        ; 忽略文本检查错误
    }
}

; 检查焦点变化
CheckFocusChanges() {
    global 上次焦点元素
    
    try {
        focusEl := UIA.GetFocusedElement()
        当前焦点 := focusEl.Name ? focusEl.Name : "未知元素"
        
        if (当前焦点 != 上次焦点元素) {
            AddLog("🔍 焦点变化: " . 上次焦点元素 . " → " . 当前焦点)
            上次焦点元素 := 当前焦点
        }
        
    } catch {
        ; 忽略焦点检查错误
    }
}

; 检查窗口状态变化
CheckWindowChanges(npEl) {
    static 上次窗口标题 := ""
    
    try {
        当前标题 := npEl.Name ? npEl.Name : ""
        
        if (当前标题 != 上次窗口标题 && 上次窗口标题 != "") {
            AddLog("🪟 窗口标题变化: " . 上次窗口标题 . " → " . 当前标题)
        }
        上次窗口标题 := 当前标题
        
    } catch {
        ; 忽略窗口检查错误
    }
}

; 测试记事本连接
TestNotepad(*) {
    try {
        if (!WinExist("ahk_exe notepad.exe")) {
            MsgBox("❌ 记事本未运行！`n请先启动记事本。")
            AddLog("测试失败: 记事本未运行")
            return
        }
        
        ; 尝试获取记事本元素
        npEl := UIA.ElementFromHandle("ahk_exe notepad.exe")
        窗口名称 := npEl.Name ? npEl.Name : "记事本"
        
        ; 尝试获取文档元素
        try {
            docEl := npEl.FindElement([{Type:"Document"}, {Type:"Edit"}])
            文本长度 := StrLen(docEl.Value ? docEl.Value : "")
            
            MsgBox("✅ 连接成功！`n窗口: " . 窗口名称 . "`n文本长度: " . 文本长度 . " 个字符")
            AddLog("测试成功: 已连接到记事本")
        } catch {
            MsgBox("✅ 窗口连接成功，但无法访问文档内容")
            AddLog("测试部分成功: 窗口可访问，文档元素不可访问")
        }
        
    } catch Error as e {
        MsgBox("❌ 连接失败: " . e.Message)
        AddLog("测试失败: " . e.Message)
    }
}

; 清空日志
ClearLog(*) {
    日志显示.Text := ""
    AddLog("日志已清空")
}

; 添加日志
AddLog(message) {
    timestamp := FormatTime(A_Now, "HH:mm:ss")
    newLog := "[" . timestamp . "] " . message . "`r`n"
    日志显示.Text .= newLog
    
    ; 自动滚动到底部
    日志显示.Focus()
    Send("^{End}")
}

; 启动程序
CreateMonitorGUI()
