#Requires AutoHotkey v2
#include UIA-v2-1.1.0\Lib\UIA.ahk
#include UIA-v2-1.1.0\Lib\UIA_Browser.ahk

/*
UIA 网页变化监控器
专门监控浏览器网页的变化
功能：
1. 监控页面标题变化
2. 监控URL变化
3. 监控页面元素变化
4. 监控加载状态
*/

; 全局变量
global 监控窗口
global 日志区域
global 监控状态 := false
global 浏览器对象
global 监控定时器
global 上次URL := ""
global 上次标题 := ""
global 上次元素数量 := 0

; 创建监控界面
CreateWebMonitorGUI() {
    global 监控窗口 := Gui("+Resize", "UIA 网页变化监控器")
    监控窗口.MarginX := 10
    监控窗口.MarginY := 10
    
    ; 标题
    监控窗口.Add("Text", "x10 y10 w500 Center", "UIA 网页变化监控器").SetFont("s14 Bold")
    
    ; 浏览器选择
    监控窗口.Add("Text", "x10 y40 w80", "浏览器:")
    browserCombo := 监控窗口.Add("ComboBox", "x100 y38 w150", ["Chrome", "Edge", "Firefox"])
    browserCombo.Text := "Chrome"
    
    ; URL输入
    监控窗口.Add("Text", "x10 y70 w80", "监控URL:")
    urlInput := 监控窗口.Add("Edit", "x100 y68 w300")
    urlInput.Text := "https://www.autohotkey.com"
    
    ; 控制按钮
    startBtn := 监控窗口.Add("Button", "x10 y100 w80 h30", "开始监控")
    stopBtn := 监控窗口.Add("Button", "x100 y100 w80 h30", "停止监控")
    clearBtn := 监控窗口.Add("Button", "x190 y100 w80 h30", "清空日志")
    navigateBtn := 监控窗口.Add("Button", "x280 y100 w80 h30", "导航到URL")
    testBtn := 监控窗口.Add("Button", "x370 y100 w80 h30", "测试连接")
    
    ; 状态显示
    statusLabel := 监控窗口.Add("Text", "x10 y140 w500", "状态: 未开始监控")
    statusLabel.SetFont("s10 Bold")
    
    ; 当前页面信息
    监控窗口.Add("Text", "x10 y170 w100", "当前页面信息:")
    pageInfo := 监控窗口.Add("Edit", "x10 y190 w500 h60 ReadOnly")
    
    ; 日志显示
    监控窗口.Add("Text", "x10 y260 w100", "变化日志:")
    global 日志区域 := 监控窗口.Add("Edit", "x10 y280 w500 h200 VScroll ReadOnly")
    
    ; 事件绑定
    startBtn.OnEvent("Click", StartWebMonitoring)
    stopBtn.OnEvent("Click", StopWebMonitoring)
    clearBtn.OnEvent("Click", ClearWebLog)
    navigateBtn.OnEvent("Click", NavigateToURL)
    testBtn.OnEvent("Click", TestBrowserConnection)
    
    监控窗口.OnEvent("Close", (*) => ExitApp())
    监控窗口.Show("w520 h500")
}

; 开始网页监控
StartWebMonitoring(*) {
    global 监控状态, 浏览器对象, 监控定时器
    
    if (监控状态) {
        AddWebLog("监控已在运行中...")
        return
    }
    
    try {
        ; 获取浏览器类型
        browserType := 监控窗口["ComboBox1"].Text
        browserExe := GetBrowserExe(browserType)
        
        ; 检查浏览器是否运行
        if (!WinExist("ahk_exe " . browserExe)) {
            MsgBox("请先启动 " . browserType . " 浏览器！")
            return
        }
        
        ; 创建浏览器对象
        浏览器对象 := UIA_Browser(browserExe)
        
        监控状态 := true
        监控定时器 := SetTimer(CheckWebChanges, 1000)  ; 每秒检查一次
        
        ; 更新状态
        UpdateStatus("状态: 正在监控 " . browserType . " 浏览器...", "Green")
        AddWebLog("开始监控 " . browserType . " 浏览器")
        
        ; 初始化状态
        InitializeWebState()
        
    } catch Error as e {
        MsgBox("启动网页监控失败: " . e.Message)
        AddWebLog("启动监控失败: " . e.Message)
    }
}

; 停止网页监控
StopWebMonitoring(*) {
    global 监控状态, 监控定时器
    
    if (!监控状态) {
        AddWebLog("监控未在运行...")
        return
    }
    
    try {
        监控状态 := false
        if (监控定时器) {
            SetTimer(监控定时器, 0)
            监控定时器 := ""
        }
        
        UpdateStatus("状态: 监控已停止", "Red")
        AddWebLog("网页监控已停止")
        
    } catch Error as e {
        MsgBox("停止监控失败: " . e.Message)
        AddWebLog("停止监控失败: " . e.Message)
    }
}

; 初始化网页状态
InitializeWebState() {
    global 上次URL, 上次标题, 上次元素数量
    
    try {
        ; 获取当前URL和标题
        上次URL := 浏览器对象.URL
        上次标题 := 浏览器对象.Title
        
        ; 获取页面元素数量
        try {
            elements := 浏览器对象.FindElements({})
            上次元素数量 := elements.Length
        } catch {
            上次元素数量 := 0
        }
        
        ; 更新页面信息显示
        UpdatePageInfo()
        AddWebLog("初始页面状态已记录")
        
    } catch Error as e {
        AddWebLog("初始化页面状态失败: " . e.Message)
    }
}

; 检查网页变化
CheckWebChanges() {
    global 上次URL, 上次标题, 上次元素数量
    
    if (!监控状态) {
        return
    }
    
    try {
        ; 检查浏览器是否还在运行
        browserType := 监控窗口["ComboBox1"].Text
        browserExe := GetBrowserExe(browserType)
        
        if (!WinExist("ahk_exe " . browserExe)) {
            AddWebLog("⚠️ 浏览器已关闭")
            StopWebMonitoring()
            return
        }
        
        ; 1. 检查URL变化
        CheckURLChange()
        
        ; 2. 检查标题变化
        CheckTitleChange()
        
        ; 3. 检查页面元素数量变化
        CheckElementCountChange()
        
        ; 更新页面信息
        UpdatePageInfo()
        
    } catch Error as e {
        AddWebLog("检查网页变化时出错: " . e.Message)
    }
}

; 检查URL变化
CheckURLChange() {
    global 上次URL
    
    try {
        当前URL := 浏览器对象.URL
        if (当前URL != 上次URL) {
            AddWebLog("🌐 URL变化: " . 上次URL . " → " . 当前URL)
            上次URL := 当前URL
        }
    } catch {
        ; 忽略URL检查错误
    }
}

; 检查标题变化
CheckTitleChange() {
    global 上次标题
    
    try {
        当前标题 := 浏览器对象.Title
        if (当前标题 != 上次标题) {
            AddWebLog("📄 标题变化: " . 上次标题 . " → " . 当前标题)
            上次标题 := 当前标题
        }
    } catch {
        ; 忽略标题检查错误
    }
}

; 检查元素数量变化
CheckElementCountChange() {
    global 上次元素数量
    
    try {
        elements := 浏览器对象.FindElements({})
        当前元素数量 := elements.Length
        
        if (当前元素数量 != 上次元素数量) {
            AddWebLog("🔢 页面元素数量变化: " . 上次元素数量 . " → " . 当前元素数量)
            上次元素数量 := 当前元素数量
        }
    } catch {
        ; 忽略元素数量检查错误
    }
}

; 导航到URL
NavigateToURL(*) {
    try {
        url := 监控窗口["Edit2"].Text
        if (url = "") {
            MsgBox("请输入URL！")
            return
        }
        
        if (!浏览器对象) {
            MsgBox("请先开始监控！")
            return
        }
        
        浏览器对象.Navigate(url)
        AddWebLog("导航到: " . url)
        
    } catch Error as e {
        MsgBox("导航失败: " . e.Message)
        AddWebLog("导航失败: " . e.Message)
    }
}

; 测试浏览器连接
TestBrowserConnection(*) {
    try {
        browserType := 监控窗口["ComboBox1"].Text
        browserExe := GetBrowserExe(browserType)
        
        if (!WinExist("ahk_exe " . browserExe)) {
            MsgBox("❌ " . browserType . " 浏览器未运行！")
            AddWebLog("测试失败: 浏览器未运行")
            return
        }
        
        ; 尝试创建浏览器对象
        testBrowser := UIA_Browser(browserExe)
        currentURL := testBrowser.URL
        currentTitle := testBrowser.Title
        
        MsgBox("✅ 连接成功！`n浏览器: " . browserType . "`nURL: " . currentURL . "`n标题: " . currentTitle)
        AddWebLog("测试成功: 已连接到 " . browserType)
        
    } catch Error as e {
        MsgBox("❌ 连接失败: " . e.Message)
        AddWebLog("测试失败: " . e.Message)
    }
}

; 获取浏览器可执行文件名
GetBrowserExe(browserType) {
    switch browserType {
        case "Chrome": return "chrome.exe"
        case "Edge": return "msedge.exe"
        case "Firefox": return "firefox.exe"
        default: return "chrome.exe"
    }
}

; 更新状态显示
UpdateStatus(text, color := "Black") {
    for control in 监控窗口 {
        if (InStr(control.Text, "状态:")) {
            control.Text := text
            control.SetFont("s10 Bold", color)
            break
        }
    }
}

; 更新页面信息显示
UpdatePageInfo() {
    try {
        info := "URL: " . 浏览器对象.URL . "`r`n"
        info .= "标题: " . 浏览器对象.Title . "`r`n"
        info .= "元素数量: " . 上次元素数量
        
        for control in 监控窗口 {
            if (control.Type = "Edit" && control.ReadOnly && InStr(control.Text, "URL:")) {
                control.Text := info
                break
            }
        }
    } catch {
        ; 忽略更新错误
    }
}

; 清空日志
ClearWebLog(*) {
    日志区域.Text := ""
    AddWebLog("日志已清空")
}

; 添加日志
AddWebLog(message) {
    timestamp := FormatTime(A_Now, "HH:mm:ss")
    newLog := "[" . timestamp . "] " . message . "`r`n"
    日志区域.Text .= newLog
    
    ; 自动滚动到底部
    日志区域.Focus()
    Send("^{End}")
}

; 启动程序
CreateWebMonitorGUI()
