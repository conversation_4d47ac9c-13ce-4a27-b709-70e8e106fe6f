#Requires AutoHotkey v2
; 引入UIA库
#include UIA-v2-1.1.0\Lib\UIA.ahk

/*
UIA-v2 使用示例和指南（修复版）
UIA（UI Automation）是微软的UI自动化框架，可以自动化现代应用程序
*/

; ========== 基础示例 ==========

; 示例1：自动化记事本
Example1_Notepad() {
    ; 启动记事本
    Run "notepad.exe"
    WinWaitActive "ahk_exe notepad.exe"
    
    try {
        ; 获取记事本窗口元素
        npEl := UIA.ElementFromHandle("ahk_exe notepad.exe")
        
        ; 查找文档编辑区域
        documentEl := npEl.FindElement([{Type:"Document"}, {Type:"Edit"}])
        
        ; 高亮显示找到的元素（用于调试）
        documentEl.Highlight()
        
        ; 设置文本内容
        documentEl.Value := "Hello from UIA!"
        
        MsgBox("成功在记事本中输入文本！")
        
    } catch Error as e {
        MsgBox("错误: " . e.Message)
    }
}

; 示例2：查找元素的不同方法
Example2_FindMethods() {
    Run "notepad.exe"
    WinWaitActive "ahk_exe notepad.exe"
    
    ; 获取窗口元素
    npEl := UIA.ElementFromHandle("ahk_exe notepad.exe")
    
    ; 方法1：按类型查找
    try {
        menuItem := npEl.FindElement({Type:"MenuItem"})
        MsgBox("找到菜单项: " . menuItem.Name)
    } catch {
        MsgBox("未找到菜单项")
    }
    
    ; 方法2：按名称查找
    try {
        fileMenu := npEl.FindElement({Name:"File"})
        if (fileMenu)
            MsgBox("找到文件菜单: " . fileMenu.Name)
    } catch {
        MsgBox("未找到文件菜单")
    }
    
    ; 方法3：查找所有匹配的元素
    try {
        allMenus := npEl.FindElements({Type:"MenuItem"})
        menuNames := ""
        for menu in allMenus {
            menuNames .= menu.Name . "`n"
        }
        if (menuNames != "")
            MsgBox("所有菜单项:`n" . menuNames)
        else
            MsgBox("未找到任何菜单项")
    } catch {
        MsgBox("查找菜单项时出错")
    }
}

; 示例3：等待元素出现
Example3_WaitElement() {
    Run "notepad.exe"
    WinWaitActive "ahk_exe notepad.exe"
    
    npEl := UIA.ElementFromHandle("ahk_exe notepad.exe")
    
    ; 等待元素出现（最多等待5秒）
    try {
        menuEl := npEl.WaitElement({Type:"MenuItem"}, 5000)
        MsgBox("成功等待到菜单元素: " . menuEl.Name)
    } catch {
        MsgBox("等待超时，未找到元素")
    }
}

; 示例4：计算器自动化
Example4_Calculator() {
    ; 启动计算器
    Run "calc.exe"
    WinWaitActive "ahk_exe Calculator.exe"
    Sleep 1000  ; 等待计算器完全加载
    
    try {
        ; 获取计算器窗口
        calcEl := UIA.ElementFromHandle("ahk_exe Calculator.exe")
        
        ; 点击数字按钮2
        btn2 := calcEl.FindElement({Name:"Two"})
        if (btn2) {
            btn2.Click()
            Sleep 200
        }
        
        ; 点击加号
        btnPlus := calcEl.FindElement({Name:"Plus"})
        if (btnPlus) {
            btnPlus.Click()
            Sleep 200
        }
        
        ; 点击数字3
        btn3 := calcEl.FindElement({Name:"Three"})
        if (btn3) {
            btn3.Click()
            Sleep 200
        }
        
        ; 点击等号
        btnEquals := calcEl.FindElement({Name:"Equals"})
        if (btnEquals) {
            btnEquals.Click()
            Sleep 500
        }
        
        MsgBox("计算器操作完成：2 + 3 = 5")
        
    } catch Error as e {
        MsgBox("计算器自动化失败: " . e.Message)
    }
}

; 示例5：元素信息获取
Example5_ElementInfo() {
    Run "notepad.exe"
    WinWaitActive "ahk_exe notepad.exe"

    try {
        npEl := UIA.ElementFromHandle("ahk_exe notepad.exe")

        ; 获取基本元素信息
        info := "窗口信息:`n"

        try {
            info .= "名称: " . npEl.Name . "`n"
        } catch {
            info .= "名称: 无法获取`n"
        }

        try {
            info .= "类型: " . npEl.Type . "`n"
        } catch {
            info .= "类型: 无法获取`n"
        }

        try {
            info .= "AutomationId: " . npEl.AutomationId . "`n"
        } catch {
            info .= "AutomationId: 无法获取`n"
        }

        try {
            rect := npEl.BoundingRectangle
            info .= "位置: x=" . rect.x . ", y=" . rect.y . "`n"
            info .= "大小: w=" . rect.width . ", h=" . rect.height . "`n"
        } catch {
            info .= "位置和大小: 无法获取`n"
        }

        ; 尝试获取子元素数量
        try {
            children := npEl.FindElements({})
            info .= "子元素数量: " . children.Length . "`n"
        } catch {
            info .= "子元素数量: 无法获取`n"
        }

        MsgBox(info)

    } catch Error as e {
        MsgBox("获取元素信息失败: " . e.Message)
    }
}

; ========== 主菜单 ==========
ShowMainMenu() {
    menu := "
    (
    UIA-v2 使用示例菜单
    
    1. 记事本自动化
    2. 查找元素方法
    3. 等待元素示例
    4. 计算器自动化
    5. 元素信息获取
    6. 退出
    
    请选择要运行的示例 (1-6):
    )"
    
    try {
        choice := InputBox(menu, "UIA示例选择", "w400 h300").Value
        
        switch choice {
            case "1":
                Example1_Notepad()
            case "2":
                Example2_FindMethods()
            case "3":
                Example3_WaitElement()
            case "4":
                Example4_Calculator()
            case "5":
                Example5_ElementInfo()
            case "6":
                ExitApp()
            default:
            {
                MsgBox("无效选择，请重新选择")
                ShowMainMenu()
                return
            }
        }
        
        ; 询问是否继续
        if (MsgBox("是否继续运行其他示例？", "继续", "YesNo") = "Yes")
            ShowMainMenu()
            
    } catch Error as e {
        MsgBox("菜单操作失败: " . e.Message)
    }
}

; 启动主菜单
ShowMainMenu()
