#Requires AutoHotkey v2
; 引入UIA库
#include UIA-v2-1.1.0\Lib\UIA.ahk

/*
UIA-v2 使用示例和指南
UIA（UI Automation）是微软的UI自动化框架，可以自动化现代应用程序
*/

; ========== 基础示例 ==========

; 示例1：自动化记事本
Example1_Notepad() {
    ; 启动记事本
    Run "notepad.exe"
    WinWaitActive "ahk_exe notepad.exe"
    
    try {
        ; 获取记事本窗口元素
        npEl := UIA.ElementFromHandle("ahk_exe notepad.exe")
        
        ; 查找文档编辑区域
        documentEl := npEl.FindElement([{Type:"Document"}, {Type:"Edit"}])
        
        ; 高亮显示找到的元素（用于调试）
        documentEl.Highlight()
        
        ; 设置文本内容
        documentEl.Value := "Hello from UIA!"
        
        MsgBox("成功在记事本中输入文本！")
        
    } catch Error as e {
        MsgBox("错误: " . e.Message)
    }
}

; 示例2：查找元素的不同方法
Example2_FindMethods() {
    Run "notepad.exe"
    WinWaitActive "ahk_exe notepad.exe"
    
    ; 获取窗口元素
    npEl := UIA.ElementFromHandle("ahk_exe notepad.exe")
    
    ; 方法1：按类型查找
    menuItem := npEl.FindElement({Type:"MenuItem"})
    MsgBox("找到菜单项: " . menuItem.Name)
    
    ; 方法2：按名称查找
    fileMenu := npEl.FindElement({Name:"File"})
    if (fileMenu)
        MsgBox("找到文件菜单: " . fileMenu.Name)
    
    ; 方法3：组合条件查找
    specificMenu := npEl.FindElement({Type:"MenuItem", Name:"File"})
    if (specificMenu)
        MsgBox("找到特定菜单: " . specificMenu.Name)
    
    ; 方法4：查找所有匹配的元素
    allMenus := npEl.FindElements({Type:"MenuItem"})
    menuNames := ""
    for menu in allMenus {
        menuNames .= menu.Name . "`n"
    }
    MsgBox("所有菜单项:`n" . menuNames)
}

; 示例3：等待元素出现
Example3_WaitElement() {
    Run "notepad.exe"
    WinWaitActive "ahk_exe notepad.exe"
    
    npEl := UIA.ElementFromHandle("ahk_exe notepad.exe")
    
    ; 等待元素出现（最多等待5秒）
    try {
        menuEl := npEl.WaitElement({Type:"MenuItem"}, 5000)
        MsgBox("成功等待到菜单元素: " . menuEl.Name)
    } catch {
        MsgBox("等待超时，未找到元素")
    }
}

; 示例4：浏览器自动化
Example4_Browser() {
    ; 需要先启动Chrome浏览器
    try {
        Run "chrome.exe"
        Sleep 2000
        
        ; 获取Chrome窗口
        chromeEl := UIA.ElementFromHandle("ahk_exe chrome.exe")
        
        ; 查找地址栏
        addressBar := chromeEl.FindElement({Type:"Edit", Name:"Address and search bar"})
        if (addressBar) {
            addressBar.SetFocus()
            addressBar.Value := "https://www.autohotkey.com"
            Send "{Enter}"
            MsgBox("成功在Chrome中导航到AutoHotkey官网！")
        }
        
    } catch Error as e {
        MsgBox("浏览器自动化失败: " . e.Message)
    }
}

; 示例5：计算器自动化
Example5_Calculator() {
    ; 启动计算器
    Run "calc.exe"
    WinWaitActive "ahk_exe Calculator.exe"
    
    try {
        ; 获取计算器窗口
        calcEl := UIA.ElementFromHandle("ahk_exe Calculator.exe")
        
        ; 点击数字按钮
        btn2 := calcEl.FindElement({Name:"Two"})
        btn2.Click()
        
        ; 点击加号
        btnPlus := calcEl.FindElement({Name:"Plus"})
        btnPlus.Click()
        
        ; 点击数字3
        btn3 := calcEl.FindElement({Name:"Three"})
        btn3.Click()
        
        ; 点击等号
        btnEquals := calcEl.FindElement({Name:"Equals"})
        btnEquals.Click()
        
        ; 获取结果
        result := calcEl.FindElement({Type:"Text", Name:"Display is 5"})
        if (result)
            MsgBox("计算结果: " . result.Name)
        
    } catch Error as e {
        MsgBox("计算器自动化失败: " . e.Message)
    }
}

; ========== 高级功能示例 ==========

; 示例6：使用条件查找
Example6_AdvancedFind() {
    Run "notepad.exe"
    WinWaitActive "ahk_exe notepad.exe"
    
    npEl := UIA.ElementFromHandle("ahk_exe notepad.exe")
    
    ; 使用OR条件查找
    element := npEl.FindElement([{Type:"Document"}, {Type:"Edit"}])
    
    ; 使用部分匹配
    partialMatch := npEl.FindElement({Name:"Bar", matchmode:"Substring"})
    
    ; 不区分大小写
    caseInsensitive := npEl.FindElement({Name:"file", casesense:0})
    
    ; 使用NOT条件
    notCondition := npEl.FindElement({Type:"MenuItem", not:{Name:"System"}})
    
    ; 查找第3个元素
    thirdElement := npEl.FindElement({Type:"MenuItem", index:3})
    
    MsgBox("高级查找条件演示完成")
}

; 示例7：元素操作
Example7_ElementOps() {
    Run "notepad.exe"
    WinWaitActive "ahk_exe notepad.exe"
    
    npEl := UIA.ElementFromHandle("ahk_exe notepad.exe")
    
    ; 获取元素信息
    MsgBox("窗口名称: " . npEl.Name)
    MsgBox("窗口类型: " . npEl.Type)
    MsgBox("窗口边界: " . npEl.BoundingRectangle.ToString())
    
    ; 检查元素状态
    if (npEl.IsEnabled)
        MsgBox("窗口已启用")
    
    if (npEl.IsVisible)
        MsgBox("窗口可见")
}

; ========== 主菜单 ==========
ShowMainMenu() {
    menu := "
    (
    UIA-v2 使用示例菜单
    
    1. 记事本自动化
    2. 查找元素方法
    3. 等待元素示例
    4. 浏览器自动化
    5. 计算器自动化
    6. 高级查找条件
    7. 元素操作示例
    8. 退出
    
    请选择要运行的示例 (1-8):
    )"
    
    choice := InputBox(menu, "UIA示例选择", "w400 h350").Value
    
    switch choice {
        case "1": Example1_Notepad()
        case "2": Example2_FindMethods()
        case "3": Example3_WaitElement()
        case "4": Example4_Browser()
        case "5": Example5_Calculator()
        case "6": Example6_AdvancedFind()
        case "7": Example7_ElementOps()
        case "8": ExitApp()
        default: {
            MsgBox("无效选择，请重新选择")
            ShowMainMenu()
        }
    }
    
    ; 询问是否继续
    if (MsgBox("是否继续运行其他示例？", "继续", "YesNo") = "Yes")
        ShowMainMenu()
}

; 启动主菜单
ShowMainMenu()
